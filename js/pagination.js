/**
 * 医院药品管理系统 - 分页组件
 * 提供完整的分页功能，支持桌面端和移动端
 */

class PaginationComponent {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            throw new Error(`Container with id "${containerId}" not found`);
        }
        
        // 默认配置
        this.options = {
            totalItems: 0,          // 总记录数
            pageSize: 20,           // 每页显示数量
            currentPage: 1,         // 当前页码
            maxPageButtons: 7,      // 最大显示页码按钮数
            type: 'desktop',        // 分页类型：desktop, mobile, compact
            showPageSizeSelector: true,  // 是否显示每页数量选择器
            showQuickJump: true,    // 是否显示快速跳转
            showTotal: true,        // 是否显示总数信息
            pageSizeOptions: [10, 20, 50, 100], // 每页数量选项
            onPageChange: null,     // 页码变化回调
            onPageSizeChange: null, // 每页数量变化回调
            ...options
        };
        
        this.totalPages = Math.ceil(this.options.totalItems / this.options.pageSize);
        this.init();
    }
    
    /**
     * 初始化分页组件
     */
    init() {
        this.render();
        this.bindEvents();
    }
    
    /**
     * 渲染分页组件
     */
    render() {
        const template = this.options.type === 'mobile' ? 
            this.getMobileTemplate() : this.getDesktopTemplate();
        
        this.container.innerHTML = template;
        this.updatePagination();
    }
    
    /**
     * 获取桌面端模板
     */
    getDesktopTemplate() {
        const compactClass = this.options.type === 'compact' ? 'compact' : '';
        
        return `
            <div class="pagination-wrapper ${compactClass}">
                ${this.options.showTotal ? `
                    <div class="pagination-info">
                        <span class="total-info">共 <span class="total-count">${this.options.totalItems}</span> 条记录</span>
                    </div>
                ` : ''}
                
                ${this.options.showPageSizeSelector ? `
                    <div class="page-size-selector">
                        <label>每页显示：</label>
                        <select class="form-select form-select-sm page-size-select">
                            ${this.options.pageSizeOptions.map(size => 
                                `<option value="${size}" ${size === this.options.pageSize ? 'selected' : ''}>${size} 条/页</option>`
                            ).join('')}
                        </select>
                    </div>
                ` : ''}
                
                <nav class="pagination-nav" aria-label="分页导航">
                    <ul class="pagination pagination-sm mb-0">
                        <li class="page-item prev-page">
                            <a class="page-link" href="javascript:void(0)" aria-label="上一页">
                                <i class="fas fa-angle-left"></i>
                                <span class="d-none d-md-inline ms-1">上一页</span>
                            </a>
                        </li>

                        <div class="page-numbers">
                            <!-- 页码按钮将在这里动态生成 -->
                        </div>

                        <li class="page-item next-page">
                            <a class="page-link" href="javascript:void(0)" aria-label="下一页">
                                <span class="d-none d-md-inline me-1">下一页</span>
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
                
                ${this.options.showQuickJump ? `
                    <div class="quick-jump">
                        <span>跳转到</span>
                        <input type="number" class="form-control form-control-sm jump-input" min="1" max="${this.totalPages}" placeholder="页码">
                        <button type="button" class="btn btn-sm btn-outline-primary jump-btn">跳转</button>
                    </div>
                ` : ''}
            </div>
        `;
    }
    
    /**
     * 获取移动端模板
     */
    getMobileTemplate() {
        return `
            <div class="pagination-wrapper mobile-pagination">
                <div class="pagination-info-mobile">
                    <span class="current-page-info">第 <span class="current-page">${this.options.currentPage}</span> 页，共 <span class="total-pages">${this.totalPages}</span> 页</span>
                </div>
                
                <nav class="pagination-nav-mobile" aria-label="移动端分页导航">
                    <ul class="pagination pagination-sm mb-0 justify-content-center">
                        <li class="page-item prev-page">
                            <a class="page-link" href="javascript:void(0)" aria-label="上一页">
                                <i class="fas fa-angle-left"></i>
                            </a>
                        </li>
                        
                        <li class="page-item active">
                            <span class="page-link current-page-display">${this.options.currentPage}</span>
                        </li>
                        
                        <li class="page-item next-page">
                            <a class="page-link" href="javascript:void(0)" aria-label="下一页">
                                <i class="fas fa-angle-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
                
                <div class="total-info-mobile">
                    <span>共 <span class="total-count">${this.options.totalItems}</span> 条</span>
                </div>
            </div>
        `;
    }
    
    /**
     * 更新分页显示
     */
    updatePagination() {
        this.totalPages = Math.ceil(this.options.totalItems / this.options.pageSize);
        
        if (this.options.type === 'mobile') {
            this.updateMobilePagination();
        } else {
            this.updateDesktopPagination();
        }
        
        this.updateButtonStates();
    }
    
    /**
     * 更新桌面端分页
     */
    updateDesktopPagination() {
        const pageNumbers = this.container.querySelector('.page-numbers');
        if (!pageNumbers) return;
        
        pageNumbers.innerHTML = this.generatePageNumbers();
        
        // 更新总数显示
        const totalCount = this.container.querySelector('.total-count');
        if (totalCount) {
            totalCount.textContent = this.options.totalItems;
        }
        
        // 更新快速跳转最大值
        const jumpInput = this.container.querySelector('.jump-input');
        if (jumpInput) {
            jumpInput.max = this.totalPages;
        }
    }
    
    /**
     * 更新移动端分页
     */
    updateMobilePagination() {
        const currentPageSpan = this.container.querySelector('.current-page');
        const totalPagesSpan = this.container.querySelector('.total-pages');
        const currentPageDisplay = this.container.querySelector('.current-page-display');
        const totalCount = this.container.querySelector('.total-count');
        
        if (currentPageSpan) currentPageSpan.textContent = this.options.currentPage;
        if (totalPagesSpan) totalPagesSpan.textContent = this.totalPages;
        if (currentPageDisplay) currentPageDisplay.textContent = this.options.currentPage;
        if (totalCount) totalCount.textContent = this.options.totalItems;
    }
    
    /**
     * 生成页码按钮
     */
    generatePageNumbers() {
        const pages = [];
        const current = this.options.currentPage;
        const total = this.totalPages;
        const maxButtons = this.options.maxPageButtons;
        
        if (total <= maxButtons) {
            // 总页数小于等于最大按钮数，显示所有页码
            for (let i = 1; i <= total; i++) {
                pages.push(this.createPageButton(i, i === current));
            }
        } else {
            // 总页数大于最大按钮数，需要省略
            const half = Math.floor(maxButtons / 2);
            let start = Math.max(1, current - half);
            let end = Math.min(total, start + maxButtons - 1);
            
            if (end - start < maxButtons - 1) {
                start = Math.max(1, end - maxButtons + 1);
            }
            
            // 添加第一页
            if (start > 1) {
                pages.push(this.createPageButton(1, false));
                if (start > 2) {
                    pages.push('<li class="page-item disabled"><span class="page-link">...</span></li>');
                }
            }
            
            // 添加中间页码
            for (let i = start; i <= end; i++) {
                pages.push(this.createPageButton(i, i === current));
            }
            
            // 添加最后一页
            if (end < total) {
                if (end < total - 1) {
                    pages.push('<li class="page-item disabled"><span class="page-link">...</span></li>');
                }
                pages.push(this.createPageButton(total, false));
            }
        }
        
        return pages.join('');
    }
    
    /**
     * 创建页码按钮
     */
    createPageButton(page, isActive) {
        const activeClass = isActive ? 'active' : '';
        return `
            <li class="page-item ${activeClass}" data-page="${page}">
                <a class="page-link" href="javascript:void(0)">${page}</a>
            </li>
        `;
    }
    
    /**
     * 更新按钮状态
     */
    updateButtonStates() {
        const prevPage = this.container.querySelector('.prev-page');
        const nextPage = this.container.querySelector('.next-page');

        // 更新上一页按钮状态
        if (prevPage) {
            if (this.options.currentPage <= 1) {
                prevPage.classList.add('disabled');
            } else {
                prevPage.classList.remove('disabled');
            }
        }

        // 更新下一页按钮状态
        if (nextPage) {
            if (this.options.currentPage >= this.totalPages) {
                nextPage.classList.add('disabled');
            } else {
                nextPage.classList.remove('disabled');
            }
        }
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 页码按钮点击事件
        this.container.addEventListener('click', (e) => {
            const target = e.target.closest('.page-item');
            if (!target || target.classList.contains('disabled')) return;

            if (target.classList.contains('prev-page')) {
                this.goToPage(this.options.currentPage - 1);
            } else if (target.classList.contains('next-page')) {
                this.goToPage(this.options.currentPage + 1);
            } else if (target.dataset.page) {
                this.goToPage(parseInt(target.dataset.page));
            }
        });

        // 每页数量选择器变化事件
        const pageSizeSelect = this.container.querySelector('.page-size-select');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                this.changePageSize(parseInt(e.target.value));
            });
        }

        // 快速跳转事件
        const jumpBtn = this.container.querySelector('.jump-btn');
        const jumpInput = this.container.querySelector('.jump-input');

        if (jumpBtn && jumpInput) {
            jumpBtn.addEventListener('click', () => {
                const page = parseInt(jumpInput.value);
                if (page && page >= 1 && page <= this.totalPages) {
                    this.goToPage(page);
                    jumpInput.value = '';
                }
            });

            jumpInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    jumpBtn.click();
                }
            });
        }
    }

    /**
     * 跳转到指定页码
     */
    goToPage(page) {
        if (page < 1 || page > this.totalPages || page === this.options.currentPage) {
            return;
        }

        this.options.currentPage = page;
        this.updatePagination();

        // 触发页码变化回调
        if (typeof this.options.onPageChange === 'function') {
            this.options.onPageChange(page, this.options.pageSize);
        }
    }

    /**
     * 改变每页显示数量
     */
    changePageSize(pageSize) {
        if (pageSize === this.options.pageSize) return;

        this.options.pageSize = pageSize;
        this.totalPages = Math.ceil(this.options.totalItems / pageSize);

        // 调整当前页码，确保不超出范围
        if (this.options.currentPage > this.totalPages) {
            this.options.currentPage = this.totalPages || 1;
        }

        this.updatePagination();

        // 触发每页数量变化回调
        if (typeof this.options.onPageSizeChange === 'function') {
            this.options.onPageSizeChange(pageSize, this.options.currentPage);
        }
    }

    /**
     * 更新总记录数
     */
    updateTotalItems(totalItems) {
        this.options.totalItems = totalItems;
        this.totalPages = Math.ceil(totalItems / this.options.pageSize);

        // 调整当前页码
        if (this.options.currentPage > this.totalPages) {
            this.options.currentPage = this.totalPages || 1;
        }

        this.updatePagination();
    }

    /**
     * 获取当前分页信息
     */
    getCurrentInfo() {
        return {
            currentPage: this.options.currentPage,
            pageSize: this.options.pageSize,
            totalItems: this.options.totalItems,
            totalPages: this.totalPages,
            startIndex: (this.options.currentPage - 1) * this.options.pageSize + 1,
            endIndex: Math.min(this.options.currentPage * this.options.pageSize, this.options.totalItems)
        };
    }

    /**
     * 重置分页到第一页
     */
    reset() {
        this.options.currentPage = 1;
        this.updatePagination();
    }

    /**
     * 销毁分页组件
     */
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
    }

    /**
     * 设置加载状态
     */
    setLoading(loading) {
        if (loading) {
            this.container.classList.add('pagination-loading');
        } else {
            this.container.classList.remove('pagination-loading');
        }
    }
}

/**
 * 工具函数：创建分页组件
 */
function createPagination(containerId, options) {
    return new PaginationComponent(containerId, options);
}

/**
 * 工具函数：响应式分页组件
 * 根据屏幕尺寸自动选择分页类型
 */
function createResponsivePagination(containerId, options = {}) {
    const getPageType = () => {
        if (window.innerWidth < 768) {
            return 'mobile';
        } else if (window.innerWidth < 992) {
            return 'compact';
        } else {
            return 'desktop';
        }
    };

    const paginationOptions = {
        ...options,
        type: getPageType()
    };

    const pagination = new PaginationComponent(containerId, paginationOptions);

    // 监听窗口大小变化
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            const newType = getPageType();
            if (newType !== pagination.options.type) {
                pagination.options.type = newType;
                pagination.render();
            }
        }, 250);
    });

    return pagination;
}

// 全局暴露
window.PaginationComponent = PaginationComponent;
window.createPagination = createPagination;
window.createResponsivePagination = createResponsivePagination;
