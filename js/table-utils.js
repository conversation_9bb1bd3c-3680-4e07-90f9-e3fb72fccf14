/**
 * 表格工具函数 - 处理表格样式规范
 * 医院药品管理系统
 */

/**
 * 初始化表格单元格溢出处理
 * 为超出长度的单元格添加title属性和menu-text类，实现悬停提示和样式控制
 */
function initTableCellOverflow() {
    // 获取所有表格
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        // 获取所有数据单元格（排除复选框列和操作列）
        const dataCells = table.querySelectorAll('td:not(:first-child):not(:last-child)');

        dataCells.forEach(cell => {
            // 排除操作列：检查单元格是否包含table-actions类或操作按钮
            if (cell.querySelector('.table-actions') || cell.classList.contains('table-actions')) {
                return; // 跳过操作列
            }

            // 检查单元格内容是否超出
            if (isCellContentOverflowing(cell)) {
                // 添加data-full-text属性用于悬停提示显示完整文字
                const fullText = cell.textContent.trim();
                if (fullText && !cell.hasAttribute('data-full-text')) {
                    cell.setAttribute('data-full-text', fullText);
                }
                // 只对超出宽度的单元格添加menu-text类
                if (!cell.classList.contains('menu-text')) {
                    cell.classList.add('menu-text');
                }
            } else {
                // 如果内容没有超出，移除menu-text类
                if (cell.classList.contains('menu-text')) {
                    cell.classList.remove('menu-text');
                }
                // 移除data-full-text属性
                if (cell.hasAttribute('data-full-text')) {
                    cell.removeAttribute('data-full-text');
                }
            }
        });
    });
}

/**
 * 检查单元格内容是否超出
 * @param {HTMLElement} cell - 表格单元格元素
 * @returns {boolean} - 是否超出
 */
function isCellContentOverflowing(cell) {
    // 创建临时元素测量文本宽度
    const tempElement = document.createElement('span');
    tempElement.style.visibility = 'hidden';
    tempElement.style.position = 'absolute';
    tempElement.style.whiteSpace = 'nowrap';
    tempElement.style.fontSize = window.getComputedStyle(cell).fontSize;
    tempElement.style.fontFamily = window.getComputedStyle(cell).fontFamily;
    tempElement.textContent = cell.textContent;

    document.body.appendChild(tempElement);
    const textWidth = tempElement.offsetWidth;
    document.body.removeChild(tempElement);

    // 获取单元格可用宽度（减去padding）
    const cellStyle = window.getComputedStyle(cell);
    const paddingLeft = parseFloat(cellStyle.paddingLeft);
    const paddingRight = parseFloat(cellStyle.paddingRight);
    const availableWidth = cell.offsetWidth - paddingLeft - paddingRight;

    return textWidth > availableWidth;
}

/**
 * 重新排列表格列顺序
 * 确保操作列在复选框/序号列后面
 */
function reorderTableColumns() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        const headerRow = table.querySelector('thead tr');
        const bodyRows = table.querySelectorAll('tbody tr');
        
        if (!headerRow) return;
        
        // 查找操作列（通常包含"操作"文字或table-actions类）
        const headers = Array.from(headerRow.children);
        let operationColumnIndex = -1;
        
        headers.forEach((header, index) => {
            const headerText = header.textContent.trim();
            if (headerText === '操作' || header.querySelector('.table-actions')) {
                operationColumnIndex = index;
            }
        });
        
        // 如果找到操作列且不在第二列位置，则移动到第二列
        if (operationColumnIndex > 1) {
            // 移动表头
            const operationHeader = headerRow.children[operationColumnIndex];
            headerRow.insertBefore(operationHeader, headerRow.children[1]);
            
            // 移动所有数据行的对应单元格
            bodyRows.forEach(row => {
                if (row.children[operationColumnIndex]) {
                    const operationCell = row.children[operationColumnIndex];
                    row.insertBefore(operationCell, row.children[1]);
                }
            });
        }
    });
}

/**
 * 清理操作列中的menu-text类
 * 确保操作列中的文字按钮不应用menu-text类
 */
function cleanupOperationColumnMenuText() {
    const tables = document.querySelectorAll('.table');

    tables.forEach(table => {
        // 查找所有包含table-actions的单元格
        const operationCells = table.querySelectorAll('td .table-actions');

        operationCells.forEach(actionContainer => {
            const cell = actionContainer.closest('td');
            if (cell) {
                // 移除操作列单元格的menu-text类
                if (cell.classList.contains('menu-text')) {
                    cell.classList.remove('menu-text');
                }
                // 移除操作列单元格的title属性
                if (cell.hasAttribute('title')) {
                    cell.removeAttribute('title');
                }

                // 移除操作列内所有链接的menu-text类
                const actionLinks = actionContainer.querySelectorAll('a');
                actionLinks.forEach(link => {
                    if (link.classList.contains('menu-text')) {
                        link.classList.remove('menu-text');
                    }
                    if (link.hasAttribute('title')) {
                        link.removeAttribute('title');
                    }
                });
            }
        });
    });
}

/**
 * 初始化表格样式规范
 * 在页面加载完成后调用
 */
function initTableStyleRules() {
    // 重新排列表格列顺序
    reorderTableColumns();

    // 清理操作列中的menu-text类
    cleanupOperationColumnMenuText();

    // 初始化单元格溢出处理
    initTableCellOverflow();

    // 监听窗口大小变化，重新计算溢出
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            initTableCellOverflow();
            cleanupOperationColumnMenuText(); // 重新清理操作列
        }, 250);
    });
}

/**
 * 动态添加表格行时调用此函数
 * 确保新添加的行也遵循样式规范，为超出宽度的单元格添加menu-text类
 * @param {HTMLElement} table - 表格元素
 */
function updateTableRowStyles(table) {
    if (!table || !table.classList.contains('table')) return;
    
    // 为新行的数据单元格添加溢出处理
    const newDataCells = table.querySelectorAll('td:not(:first-child):not(:last-child)');

    newDataCells.forEach(cell => {
        // 排除操作列：检查单元格是否包含table-actions类或操作按钮
        if (cell.querySelector('.table-actions') || cell.classList.contains('table-actions')) {
            return; // 跳过操作列
        }

        if (isCellContentOverflowing(cell)) {
            const fullText = cell.textContent.trim();
            if (fullText && !cell.hasAttribute('title')) {
                cell.setAttribute('title', fullText);
            }
            // 只对超出宽度的单元格添加menu-text类
            if (!cell.classList.contains('menu-text')) {
                cell.classList.add('menu-text');
            }
        } else {
            // 如果内容没有超出，移除menu-text类
            if (cell.classList.contains('menu-text')) {
                cell.classList.remove('menu-text');
            }
            // 移除title属性
            if (cell.hasAttribute('title')) {
                cell.removeAttribute('title');
            }
        }
    });
}

/**
 * 为表格添加序号列
 * 在复选框列后面添加递增序号
 */
function addTableRowNumbers() {
    const tables = document.querySelectorAll('.table');
    
    tables.forEach(table => {
        const headerRow = table.querySelector('thead tr');
        const bodyRows = table.querySelectorAll('tbody tr');
        
        if (!headerRow) return;
        
        // 检查是否已经有序号列
        const headers = Array.from(headerRow.children);
        let hasNumberColumn = false;
        
        headers.forEach((header, index) => {
            if (index === 1 && header.textContent.trim() === '序号') {
                hasNumberColumn = true;
            }
        });
        
        // 如果没有序号列，则添加
        if (!hasNumberColumn) {
            // 添加表头的序号列
            const numberHeader = document.createElement('th');
            numberHeader.textContent = '序号';
            headerRow.insertBefore(numberHeader, headerRow.children[1]);
            
            // 为每一行添加序号单元格
            bodyRows.forEach((row, index) => {
                const numberCell = document.createElement('td');
                numberCell.textContent = index + 1;
                numberCell.classList.add('row-number');
                row.insertBefore(numberCell, row.children[1]);
            });
        } else {
            // 如果已有序号列，只更新序号
            bodyRows.forEach((row, index) => {
                if (row.children[1] && row.children[1].classList.contains('row-number')) {
                    row.children[1].textContent = index + 1;
                }
            });
        }
    });
}

/**
 * 表格工具函数集合
 */
const TableUtils = {
    init: initTableStyleRules,
    updateRow: updateTableRowStyles,
    checkOverflow: initTableCellOverflow,
    reorderColumns: reorderTableColumns,
    addRowNumbers: addTableRowNumbers,
    cleanupOperationMenuText: cleanupOperationColumnMenuText
};

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，确保所有样式都已加载
    setTimeout(() => {
        TableUtils.init();
        TableUtils.addRowNumbers(); // 添加序号列
    }, 100);
});

// 全局暴露
window.TableUtils = TableUtils;
