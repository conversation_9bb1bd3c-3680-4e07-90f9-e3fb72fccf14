<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页组件内联测试 - ant-card-body内右侧显示</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/pagination.css">
    
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .demo-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .demo-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 0;
            color: #333;
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #e0e0e0;
        }
        .ant-card-body {
            padding: 20px;
            background: white;
        }
        .table-container {
            margin-bottom: 0;
        }
        /* 模拟表格样式 */
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
        }
        .demo-table th,
        .demo-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            text-align: left;
        }
        .demo-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .demo-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>分页组件内联测试 - ant-card-body内右侧显示</h2>
        
        <div class="test-info">
            <h5>测试内容：</h5>
            <ul>
                <li>分页组件显示在 ant-card-body 区域内</li>
                <li>位于表格下方右侧</li>
                <li>在各个屏幕尺寸下始终保持一行显示</li>
                <li>移除了首页和末页按钮</li>
                <li>布局更紧凑，间距更小</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="demo-title">桌面端效果（宽屏）</div>
            <div class="ant-card-body">
                <div class="table-container">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>示例数据1</td>
                                <td>类型A</td>
                                <td>正常</td>
                                <td>2024-01-15</td>
                                <td>查看 编辑 删除</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>示例数据2</td>
                                <td>类型B</td>
                                <td>正常</td>
                                <td>2024-01-14</td>
                                <td>查看 编辑 删除</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>示例数据3</td>
                                <td>类型C</td>
                                <td>停用</td>
                                <td>2024-01-13</td>
                                <td>查看 编辑 删除</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination-container" id="desktopPagination">
                    <!-- 分页组件将在这里生成 -->
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">平板端效果（中等屏幕）</div>
            <div class="ant-card-body" style="max-width: 768px; margin: 0 auto;">
                <div class="table-container">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>名称</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>示例数据1</td>
                                <td>正常</td>
                                <td>查看 编辑</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>示例数据2</td>
                                <td>正常</td>
                                <td>查看 编辑</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination-container" id="tabletPagination">
                    <!-- 分页组件将在这里生成 -->
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">移动端效果（小屏幕）</div>
            <div class="ant-card-body" style="max-width: 375px; margin: 0 auto;">
                <div class="table-container">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>示例数据1</td>
                                <td>正常</td>
                                <td>查看</td>
                            </tr>
                            <tr>
                                <td>示例数据2</td>
                                <td>正常</td>
                                <td>查看</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination-container" id="mobilePagination">
                    <!-- 分页组件将在这里生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/pagination.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 桌面端分页组件
            const desktopPagination = createResponsivePagination('desktopPagination', {
                totalItems: 1250,
                pageSize: 20,
                currentPage: 5,
                onPageChange: (page, pageSize) => {
                    console.log('桌面端分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('桌面端分页 - 每页数量变化:', pageSize, currentPage);
                }
            });

            // 平板端分页组件
            const tabletPagination = createResponsivePagination('tabletPagination', {
                totalItems: 500,
                pageSize: 20,
                currentPage: 3,
                onPageChange: (page, pageSize) => {
                    console.log('平板端分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('平板端分页 - 每页数量变化:', pageSize, currentPage);
                }
            });

            // 移动端分页组件
            const mobilePagination = createResponsivePagination('mobilePagination', {
                totalItems: 200,
                pageSize: 20,
                currentPage: 2,
                onPageChange: (page, pageSize) => {
                    console.log('移动端分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('移动端分页 - 每页数量变化:', pageSize, currentPage);
                }
            });
        });
    </script>
</body>
</html>
