<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页组件最终间距测试 - 上下都是10px</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/pagination.css">
    
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #4caf50;
        }
        .demo-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .demo-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 0;
            color: #333;
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #e0e0e0;
        }
        /* 测量工具 */
        .measurement-overlay {
            position: relative;
        }
        .measurement-overlay::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 100;
        }
        .measure-line {
            position: absolute;
            background: red;
            z-index: 101;
        }
        .measure-line.horizontal {
            height: 1px;
            left: 0;
            right: 0;
        }
        .measure-line.vertical {
            width: 1px;
            top: 0;
            bottom: 0;
        }
        .measure-text {
            position: absolute;
            background: red;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            z-index: 102;
            white-space: nowrap;
        }
        /* 模拟表格样式 */
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
        }
        .demo-table th,
        .demo-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            text-align: left;
        }
        .demo-table th {
            background-color: #E7EFFF;
            font-weight: 600;
        }
        .demo-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>分页组件最终间距测试 - 上下都是10px</h2>
        
        <div class="test-info">
            <h5>✅ 修改完成：</h5>
            <ul>
                <li><strong>分页组件上方</strong>：距离表格底部 10px</li>
                <li><strong>分页组件下方</strong>：距离 ant-card-body 底部 10px</li>
                <li><strong>实现方式</strong>：ant-card-body 底部内边距调整为 10px，分页组件底部外边距为 0</li>
                <li><strong>响应式</strong>：移动端也保持相同的 10px 间距</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="demo-title">桌面端效果 - 最终版本</div>
            <div class="ant-card-body measurement-overlay">
                <div class="table-container">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>供应商名称</th>
                                <th>供应商类型</th>
                                <th>联系人</th>
                                <th>联系电话</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>华北制药股份有限公司</td>
                                <td>生产厂家</td>
                                <td>张经理</td>
                                <td>010-12345678</td>
                                <td><span style="color: #10B981;">正常</span></td>
                                <td>查看 编辑 删除</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>石药集团欧意药业有限公司</td>
                                <td>生产厂家</td>
                                <td>李总监</td>
                                <td>0311-87654321</td>
                                <td><span style="color: #10B981;">正常</span></td>
                                <td>查看 编辑 删除</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>华润双鹤药业股份有限公司</td>
                                <td>生产厂家</td>
                                <td>王主任</td>
                                <td>021-98765432</td>
                                <td><span style="color: #6B7280;">停用</span></td>
                                <td>查看 编辑 删除</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination-container" id="finalTestPagination">
                    <!-- 分页组件将在这里生成 -->
                </div>
                
                <!-- 测量线 -->
                <div class="measure-line horizontal" style="top: calc(100% - 10px);"></div>
                <div class="measure-text" style="bottom: 5px; right: 10px;">距离底部 10px</div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">移动端效果 - 响应式测试</div>
            <div class="ant-card-body" style="max-width: 375px; margin: 0 auto;">
                <div class="table-container">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>华北制药股份有限公司</td>
                                <td><span style="color: #10B981;">正常</span></td>
                                <td>查看</td>
                            </tr>
                            <tr>
                                <td>石药集团欧意药业</td>
                                <td><span style="color: #10B981;">正常</span></td>
                                <td>查看</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination-container" id="mobileTestPagination">
                    <!-- 分页组件将在这里生成 -->
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">技术说明</div>
            <div style="padding: 20px; background: #f8f9fa;">
                <h6>实现原理：</h6>
                <ul style="margin-bottom: 0;">
                    <li><code>.ant-card-body</code> 的 padding 调整为 <code>24px 24px 10px 24px</code></li>
                    <li><code>.pagination-container</code> 的 margin 调整为 <code>10px 0 0 0</code></li>
                    <li>移动端 <code>.ant-card-body</code> 的 padding 调整为 <code>16px 16px 10px 16px</code></li>
                    <li>这样确保了分页组件上方10px（margin-top）+ 下方10px（ant-card-body的padding-bottom）</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/pagination.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 桌面端最终测试分页组件
            const finalTestPagination = createResponsivePagination('finalTestPagination', {
                totalItems: 1250,
                pageSize: 20,
                currentPage: 5,
                onPageChange: (page, pageSize) => {
                    console.log('最终测试分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('最终测试分页 - 每页数量变化:', pageSize, currentPage);
                }
            });

            // 移动端测试分页组件
            const mobileTestPagination = createResponsivePagination('mobileTestPagination', {
                totalItems: 200,
                pageSize: 20,
                currentPage: 2,
                onPageChange: (page, pageSize) => {
                    console.log('移动端测试分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('移动端测试分页 - 每页数量变化:', pageSize, currentPage);
                }
            });
        });
    </script>
</body>
</html>
