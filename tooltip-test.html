<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格Tooltip测试</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/global.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .debug-info {
            background: #fff3e0;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>表格Tooltip测试页面</h2>
        
        <div class="test-info">
            <h5>测试说明：</h5>
            <p>这个页面用来测试表格中tooltip提示是否正确显示单元格的实际文字内容。</p>
            <ul>
                <li>鼠标悬停在超长文本的单元格上，应该显示完整的文字内容</li>
                <li>tooltip显示的内容应该是单元格的实际文字，而不是其他内容</li>
                <li>短文本的单元格不应该显示tooltip</li>
            </ul>
        </div>

        <div class="ant-card-body">
            <table class="table table-hover" id="tooltipTestTable">
                <thead>
                    <tr>
                        <th><input type="checkbox"></th>
                        <th>序号</th>
                        <th>短文本</th>
                        <th>中等长度文本</th>
                        <th>超长文本测试列</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td class="row-number">1</td>
                        <td>短</td>
                        <td>中等长度的文本</td>
                        <td>这是一个非常非常长的文本内容，专门用来测试当单元格文字超出最大宽度时，鼠标悬停是否能正确显示完整的文字内容作为tooltip提示</td>
                        <td><span class="status-badge active">正常</span></td>
                        <td>
                            <div class="table-actions">
                                <a href="#">查看</a>
                                <a href="#">编辑</a>
                                <a href="#" class="text-danger">删除</a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td class="row-number">2</td>
                        <td>测试</td>
                        <td>普通文本内容</td>
                        <td>另一个超长的文本内容示例，这个文本应该会被截断并显示省略号，当鼠标悬停时应该显示这段完整的文字内容</td>
                        <td><span class="status-badge pending">待审核</span></td>
                        <td>
                            <div class="table-actions">
                                <a href="#">查看</a>
                                <a href="#">编辑</a>
                                <a href="#" class="text-danger">删除</a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td class="row-number">3</td>
                        <td>简短</td>
                        <td>正常长度</td>
                        <td>第三行的超长文本内容，用于验证tooltip功能是否在所有行中都能正常工作，显示的应该是这段文字的完整内容</td>
                        <td><span class="status-badge inactive">停用</span></td>
                        <td>
                            <div class="table-actions">
                                <a href="#">查看</a>
                                <a href="#">编辑</a>
                                <a href="#" class="text-danger">删除</a>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="debug-info">
            <h5>调试信息：</h5>
            <p>打开浏览器开发者工具，查看控制台输出的调试信息。</p>
            <div id="debugOutput"></div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/table-utils.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== 表格Tooltip测试开始 ===');
            
            // 延迟检查，确保table-utils.js已经处理完成
            setTimeout(() => {
                const table = document.getElementById('tooltipTestTable');
                const dataCells = table.querySelectorAll('td:not(:first-child):not(:last-child)');
                
                console.log('检查表格单元格的tooltip设置：');
                
                let debugHtml = '<h6>单元格检查结果：</h6><ul>';
                
                dataCells.forEach((cell, index) => {
                    // 跳过操作列
                    if (cell.querySelector('.table-actions') || cell.classList.contains('table-actions')) {
                        return;
                    }
                    
                    const cellText = cell.textContent.trim();
                    const hasTitle = cell.hasAttribute('title');
                    const titleValue = cell.getAttribute('title');
                    const hasMenuText = cell.classList.contains('menu-text');
                    
                    console.log(`单元格 ${index + 1}:`, {
                        text: cellText,
                        hasTitle: hasTitle,
                        titleValue: titleValue,
                        hasMenuText: hasMenuText,
                        textLength: cellText.length
                    });
                    
                    debugHtml += `<li>
                        <strong>单元格 ${index + 1}:</strong><br>
                        文字内容: "${cellText}"<br>
                        文字长度: ${cellText.length}<br>
                        有title属性: ${hasTitle}<br>
                        title属性值: "${titleValue || '无'}"<br>
                        有menu-text类: ${hasMenuText}<br>
                        <small style="color: ${hasTitle && titleValue === cellText ? 'green' : 'red'}">
                            ${hasTitle && titleValue === cellText ? '✓ tooltip内容正确' : '✗ tooltip内容不正确或缺失'}
                        </small>
                    </li>`;
                });
                
                debugHtml += '</ul>';
                document.getElementById('debugOutput').innerHTML = debugHtml;
                
                console.log('=== 表格Tooltip测试完成 ===');
            }, 300);
        });
    </script>
</body>
</html>
