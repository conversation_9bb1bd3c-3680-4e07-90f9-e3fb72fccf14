<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>供应商列表 - 医院药品管理子系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../css/layout.css">
    <link rel="stylesheet" href="../../css/global.css">
    <link rel="stylesheet" href="../../css/pagination.css">
    
    <!-- 强制设置CSS变量以确保浏览器兼容性 -->
    <style>
        :root {
            --primary-color: #145EFF !important;
            --primary-color-light: rgba(20, 94, 255, 0.1) !important;
            --danger-color-light: rgba(239, 68, 68, 0.1) !important;
        }
        
        .logo-icon,
        .menu-item.active .menu-link,
        .tab-item.active {
            color: #145EFF !important;
        }
        
        .menu-link:hover,
        .menu-item.active .menu-link,
        .tab-item:not(.active):hover,
        .submenu a:hover,
        .table-actions a:hover {
            background-color: rgba(20, 94, 255, 0.1) !important;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 左侧导航区域 -->
        <div class="left">
            <!-- Logo区域 -->
            <div class="logo-area">
                <div class="logo-content">
                    <i class="fas fa-hospital-alt logo-icon"></i>
                    <span class="logo-text">药品管理系统</span>
                </div>
            </div>
            
            <!-- 左侧导航菜单 -->
            <div class="left-nav">
                <nav class="nav-menu">
                    <ul class="menu-list">
                        <li class="menu-item">
                            <a href="../../dashboard.html" class="menu-link">
                                <i class="fas fa-home menu-icon"></i>
                                <span class="menu-text">首页</span>
                            </a>
                        </li>
                        <li class="menu-item">
                            <a href="javascript:void(0)" class="menu-link" data-submenu="drugs">
                                <i class="fas fa-pills menu-icon"></i>
                                <span class="menu-text">药品管理</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu">
                                <li><a href="../drugs/drug-list.html">药品列表</a></li>
                                <li><a href="../drugs/drug-add.html">新增药品</a></li>
                                <li><a href="../drugs/drug-category.html">药品分类</a></li>
                            </ul>
                        </li>
                        <li class="menu-item active expanded">
                            <a href="javascript:void(0)" class="menu-link" data-submenu="suppliers">
                                <i class="fas fa-building menu-icon"></i>
                                <span class="menu-text">供应商管理</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu">
                                <li><a href="supplier-list.html" class="active">供应商列表</a></li>
                                <li><a href="supplier-add.html">新增供应商</a></li>
                            </ul>
                        </li>
                        <li class="menu-item">
                            <a href="javascript:void(0)" class="menu-link" data-submenu="relations">
                                <i class="fas fa-link menu-icon"></i>
                                <span class="menu-text">关联管理</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu">
                                <li><a href="../relations/relation-list.html">关联列表</a></li>
                                <li><a href="../relations/relation-add.html">新增关联</a></li>
                            </ul>
                        </li>
                        <li class="menu-item">
                            <a href="javascript:void(0)" class="menu-link" data-submenu="data">
                                <i class="fas fa-database menu-icon"></i>
                                <span class="menu-text">数据管理</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu">
                                <li><a href="../data/data-import.html">数据导入</a></li>
                                <li><a href="../data/data-export.html">数据导出</a></li>
                            </ul>
                        </li>
                        <li class="menu-item">
                            <a href="javascript:void(0)" class="menu-link" data-submenu="statistics">
                                <i class="fas fa-chart-bar menu-icon"></i>
                                <span class="menu-text">统计分析</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu">
                                <li><a href="../statistics/drug-statistics.html">药品统计</a></li>
                                <li><a href="../statistics/supplier-statistics.html">供应商统计</a></li>
                            </ul>
                        </li>
                        <li class="menu-item">
                            <a href="javascript:void(0)" class="menu-link" data-submenu="system">
                                <i class="fas fa-cog menu-icon"></i>
                                <span class="menu-text">系统管理</span>
                                <i class="fas fa-chevron-down menu-arrow"></i>
                            </a>
                            <ul class="submenu">
                                <li><a href="../system/operation-log.html">操作日志</a></li>
                                <li><a href="../system/system-settings.html">系统设置</a></li>
                            </ul>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        
        <!-- 右侧内容区域 -->
        <div class="right">
            <!-- 顶部导航栏 -->
            <div class="top-nav">
                <div class="nav-left">
                    <button class="menu-toggle-btn" id="menuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="breadcrumb-tabs">
                        <button class="tab-nav-btn tab-nav-left" id="tabNavLeft" style="display: none;">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="tabs-scroll-area">
                            <div class="tab-item" data-page="dashboard">
                                <span class="tab-text">首页</span>
                                <i class="fas fa-times tab-close"></i>
                            </div>
                            <div class="tab-item active" data-page="supplier-list">
                                <span class="tab-text">供应商列表</span>
                                <i class="fas fa-times tab-close"></i>
                            </div>
                        </div>
                        <button class="tab-nav-btn tab-nav-right" id="tabNavRight" style="display: none;">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                <div class="nav-right">
                    <div class="user-info">
                        <div class="user-avatar">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <div class="user-dropdown">
                            <span class="username">管理员</span>
                            <i class="fas fa-chevron-down"></i>
                            <div class="dropdown-menu">
                                <a href="javascript:void(0)" class="dropdown-item" onclick="logout()">
                                    <i class="fas fa-sign-out-alt"></i>
                                    退出登录
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content">
                <!-- 页面头部 -->
                <div class="page-header">
                    <div class="page-actions" id="pageActions">
                        <a href="supplier-add.html" class="btn btn-primary">
                            <i class="fas fa-plus"></i> 新增供应商
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportSuppliers()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <button type="button" class="btn btn-info" onclick="batchImport()">
                            <i class="fas fa-upload"></i> 批量导入
                        </button>
                    </div>
                    <div class="page-filters" id="pageFilters">
                        <select class="form-select" id="typeFilter">
                            <option value="">全部类型</option>
                            <option value="manufacturer">生产厂家</option>
                            <option value="distributor">经销商</option>
                            <option value="agent">代理商</option>
                        </select>
                        <select class="form-select" id="statusFilter">
                            <option value="">全部状态</option>
                            <option value="active">正常</option>
                            <option value="inactive">停用</option>
                        </select>
                        <div class="search-box">
                            <input type="text" class="form-control search-input" placeholder="搜索供应商名称、联系人..." id="searchInput">
                            <button type="button" class="btn btn-light" onclick="searchSuppliers()">搜索</button>
                        </div>
                    </div>
                </div>
                
                <!-- 页面内容 -->
                <div class="ant-card-body" id="pageContent">
                    <div class="table-container">
                        <table class="table table-hover" id="supplierTable">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>供应商名称</th>
                                    <th>供应商类型</th>
                                    <th>联系人</th>
                                    <th>联系电话</th>
                                    <th>邮箱</th>
                                    <th>地址</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox" value="1"></td>
                                    <td>华北制药股份有限公司华北制药股份有限公司华北制药股份有限公司华北制药股份有限公司华北制药股份有限公司</td>
                                    <td>生产厂家</td>
                                    <td>张经理</td>
                                    <td>010-12345678</td>
                                    <td><EMAIL></td>
                                    <td>北京市朝阳区</td>
                                    <td><span class="status-badge active">正常</span></td>
                                    <td>2024-01-15</td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="supplier-detail.html?id=1">查看</a>
                                            <a href="supplier-edit.html?id=1">编辑</a>
                                            <a href="javascript:void(0)" onclick="deleteSupplier(1)" class="text-danger">删除</a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" value="2"></td>
                                    <td>石药集团欧意药业有限公司</td>
                                    <td>生产厂家</td>
                                    <td>李总监</td>
                                    <td>0311-87654321</td>
                                    <td><EMAIL></td>
                                    <td>河北省石家庄市</td>
                                    <td><span class="status-badge active">正常</span></td>
                                    <td>2024-01-14</td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="supplier-detail.html?id=2">查看</a>
                                            <a href="supplier-edit.html?id=2">编辑</a>
                                            <a href="javascript:void(0)" onclick="deleteSupplier(2)" class="text-danger">删除</a>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" value="3"></td>
                                    <td>华润双鹤药业股份有限公司</td>
                                    <td>生产厂家</td>
                                    <td>王主任</td>
                                    <td>021-98765432</td>
                                    <td><EMAIL></td>
                                    <td>上海市浦东新区</td>
                                    <td><span class="status-badge inactive">停用</span></td>
                                    <td>2024-01-13</td>
                                    <td>
                                        <div class="table-actions">
                                            <a href="supplier-detail.html?id=3">查看</a>
                                            <a href="supplier-edit.html?id=3">编辑</a>
                                            <a href="javascript:void(0)" onclick="deleteSupplier(3)" class="text-danger">删除</a>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页区域 -->
                    <div class="pagination-container" id="paginationArea">
                        <!-- 分页组件将在这里加载 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
    
    <!-- Custom JavaScript -->
    <script src="../../js/layout.js"></script>
    <script src="../../js/global.js"></script>
    <script src="../../js/pagination.js"></script>
    <script src="../../js/table-utils.js"></script>
    
    <!-- 页面特定脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化分页组件
            const pagination = createResponsivePagination('paginationArea', {
                totalItems: 56,
                pageSize: 20,
                currentPage: 1,
                onPageChange: (page, pageSize) => {
                    console.log('页码变化:', page, pageSize);
                    loadSupplierData(page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('每页数量变化:', pageSize, currentPage);
                    loadSupplierData(currentPage, pageSize);
                }
            });
            
            // 全选功能
            document.getElementById('selectAll').addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('#supplierTable tbody input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        });
        
        // 搜索供应商
        function searchSuppliers() {
            const searchTerm = document.getElementById('searchInput').value;
            const type = document.getElementById('typeFilter').value;
            const status = document.getElementById('statusFilter').value;
            
            console.log('搜索条件:', { searchTerm, type, status });
            // 这里添加搜索逻辑
            Utils.showMessage('搜索功能开发中...', 'info');
        }
        
        // 导出供应商数据
        function exportSuppliers() {
            Utils.showMessage('正在导出数据...', 'info');
            // 这里添加导出逻辑
        }
        
        // 批量导入
        function batchImport() {
            window.location.href = '../data/data-import.html';
        }
        
        // 删除供应商
        function deleteSupplier(id) {
            Utils.showConfirm('确定要删除这个供应商吗？', () => {
                console.log('删除供应商:', id);
                Utils.showMessage('删除成功', 'success');
                // 这里添加删除逻辑
            });
        }
        
        // 加载供应商数据
        function loadSupplierData(page, pageSize) {
            console.log('加载供应商数据:', page, pageSize);
            // 这里添加数据加载逻辑
        }
    </script>
</body>
</html>
