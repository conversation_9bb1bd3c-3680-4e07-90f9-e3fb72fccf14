<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页组件测试 - 修改后效果</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/pagination.css">
    
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .demo-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>分页组件修改后效果测试</h2>
        
        <div class="test-info">
            <h5>修改内容：</h5>
            <ul>
                <li>移除了首页和末页按钮</li>
                <li>调整为右端对齐</li>
                <li>缩小了各区域间的间距，让布局更紧凑</li>
                <li>减小了按钮的内边距和间距</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="demo-title">桌面端分页组件（数据较少）</div>
            <div class="pagination-container" id="desktopPagination1">
                <!-- 分页组件将在这里生成 -->
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">桌面端分页组件（数据较多）</div>
            <div class="pagination-container" id="desktopPagination2">
                <!-- 分页组件将在这里生成 -->
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">紧凑型分页组件</div>
            <div class="pagination-container" id="compactPagination">
                <!-- 分页组件将在这里生成 -->
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">移动端分页组件</div>
            <div class="pagination-container" id="mobilePagination">
                <!-- 分页组件将在这里生成 -->
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">响应式分页组件（自动适配）</div>
            <div class="pagination-container" id="responsivePagination">
                <!-- 分页组件将在这里生成 -->
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/pagination.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 桌面端分页组件（数据较少）
            const desktopPagination1 = new PaginationComponent('desktopPagination1', {
                totalItems: 56,
                pageSize: 20,
                currentPage: 1,
                type: 'desktop',
                onPageChange: (page, pageSize) => {
                    console.log('桌面端分页1 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('桌面端分页1 - 每页数量变化:', pageSize, currentPage);
                }
            });

            // 桌面端分页组件（数据较多）
            const desktopPagination2 = new PaginationComponent('desktopPagination2', {
                totalItems: 1250,
                pageSize: 20,
                currentPage: 5,
                type: 'desktop',
                onPageChange: (page, pageSize) => {
                    console.log('桌面端分页2 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('桌面端分页2 - 每页数量变化:', pageSize, currentPage);
                }
            });

            // 紧凑型分页组件
            const compactPagination = new PaginationComponent('compactPagination', {
                totalItems: 500,
                pageSize: 20,
                currentPage: 3,
                type: 'compact',
                onPageChange: (page, pageSize) => {
                    console.log('紧凑型分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('紧凑型分页 - 每页数量变化:', pageSize, currentPage);
                }
            });

            // 移动端分页组件
            const mobilePagination = new PaginationComponent('mobilePagination', {
                totalItems: 300,
                pageSize: 20,
                currentPage: 2,
                type: 'mobile',
                onPageChange: (page, pageSize) => {
                    console.log('移动端分页 - 页码变化:', page, pageSize);
                }
            });

            // 响应式分页组件
            const responsivePagination = createResponsivePagination('responsivePagination', {
                totalItems: 800,
                pageSize: 20,
                currentPage: 4,
                onPageChange: (page, pageSize) => {
                    console.log('响应式分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('响应式分页 - 每页数量变化:', pageSize, currentPage);
                }
            });
        });
    </script>
</body>
</html>
