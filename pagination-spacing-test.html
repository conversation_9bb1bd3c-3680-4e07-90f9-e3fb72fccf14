<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页组件间距测试 - 10px间隙</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/global.css">
    <link rel="stylesheet" href="css/pagination.css">
    
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        .demo-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        .demo-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 0;
            color: #333;
            background: #f8f9fa;
            padding: 12px 16px;
            border-bottom: 1px solid #e0e0e0;
        }
        /* 添加边框来可视化间距 */
        .ant-card-body {
            border: 2px dashed #ff9800 !important;
            position: relative;
        }
        .ant-card-body::before {
            content: 'ant-card-body 区域';
            position: absolute;
            top: 2px;
            left: 2px;
            background: #ff9800;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
        }
        .table-container {
            border: 2px dashed #4caf50 !important;
            position: relative;
        }
        .table-container::before {
            content: 'table-container 区域';
            position: absolute;
            top: 2px;
            left: 2px;
            background: #4caf50;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            z-index: 10;
        }
        .pagination-container {
            border: 2px dashed #2196f3 !important;
            position: relative;
        }
        .pagination-container::before {
            content: 'pagination-container 区域';
            position: absolute;
            top: 2px;
            left: 2px;
            background: #2196f3;
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            z-index: 10;
        }
        /* 模拟表格样式 */
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
        }
        .demo-table th,
        .demo-table td {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            text-align: left;
        }
        .demo-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .demo-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        /* 间距测量工具 */
        .spacing-ruler {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 0, 0, 0.8);
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 2px;
            z-index: 20;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>分页组件间距测试 - 10px间隙验证</h2>
        
        <div class="test-info">
            <h5>测试目标：</h5>
            <ul>
                <li>分页组件上方距离表格底部：10px</li>
                <li>分页组件下方距离 ant-card-body 底部：10px</li>
                <li>移除所有多余的间距</li>
                <li>彩色虚线边框用于可视化各个区域</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="demo-title">间距测试效果</div>
            <div class="ant-card-body">
                <div class="table-container">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>示例数据1</td>
                                <td>类型A</td>
                                <td>正常</td>
                                <td>2024-01-15</td>
                                <td>查看 编辑 删除</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>示例数据2</td>
                                <td>类型B</td>
                                <td>正常</td>
                                <td>2024-01-14</td>
                                <td>查看 编辑 删除</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>示例数据3</td>
                                <td>类型C</td>
                                <td>停用</td>
                                <td>2024-01-13</td>
                                <td>查看 编辑 删除</td>
                            </tr>
                        </tbody>
                    </table>
                    <div class="spacing-ruler">表格底部</div>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination-container" id="spacingTestPagination">
                    <div class="spacing-ruler">分页区域</div>
                    <!-- 分页组件将在这里生成 -->
                </div>
                <div class="spacing-ruler" style="position: absolute; bottom: 10px; right: 10px; top: auto; transform: none;">ant-card-body底部</div>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">实际使用效果（无边框）</div>
            <div class="ant-card-body" style="border: 1px solid #e0e0e0 !important;">
                <div class="table-container" style="border: 1px solid #E5E7EB !important;">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>名称</th>
                                <th>类型</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>正常使用效果示例</td>
                                <td>类型A</td>
                                <td>正常</td>
                                <td>查看 编辑</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>间距已调整为10px</td>
                                <td>类型B</td>
                                <td>正常</td>
                                <td>查看 编辑</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页区域 -->
                <div class="pagination-container" id="normalTestPagination" style="border: none !important;">
                    <!-- 分页组件将在这里生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/pagination.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 间距测试分页组件
            const spacingTestPagination = createResponsivePagination('spacingTestPagination', {
                totalItems: 500,
                pageSize: 20,
                currentPage: 3,
                onPageChange: (page, pageSize) => {
                    console.log('间距测试分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('间距测试分页 - 每页数量变化:', pageSize, currentPage);
                }
            });

            // 正常使用效果分页组件
            const normalTestPagination = createResponsivePagination('normalTestPagination', {
                totalItems: 200,
                pageSize: 20,
                currentPage: 2,
                onPageChange: (page, pageSize) => {
                    console.log('正常效果分页 - 页码变化:', page, pageSize);
                },
                onPageSizeChange: (pageSize, currentPage) => {
                    console.log('正常效果分页 - 每页数量变化:', pageSize, currentPage);
                }
            });
        });
    </script>
</body>
</html>
