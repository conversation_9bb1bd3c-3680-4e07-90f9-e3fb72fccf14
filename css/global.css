/* 医院药品管理系统 - 全局样式 */



/* Bootstrap卡片覆盖 */
.card.bg-white,
.card-header.bg-white,
.card-body.bg-white {
    background-color: #FFFFFF !important;
}



/* 页面布局间距 */
.page-header {
    margin-bottom: 24px; /* 页面头部与内容区域的间距 */
}

.ant-card-body {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    margin-bottom: 20px; /* 内容区域底部间距 */
}

/* 分页组件间距 */
.pagination-container {
    margin-top: 10px; /* 分页组件与内容区域的间距 */
    margin-bottom: 10px;
}

/* 表单区域间距 */
.form-section {
    margin-bottom: 24px; /* 表单区块之间的间距 */
}

.form-actions {
    margin-top: 32px; /* 表单操作按钮与内容的间距 */
}

/* 统计卡片样式 */
.stat-card {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), #3B82F6);
}

.stat-icon i {
    font-size: 24px;
}

.stat-info h3 {
    font-size: 28px;
    font-weight: 700;
    color: var(--nav-text-color);
    margin: 0 0 4px 0;
}

.stat-info p {
    font-size: 14px;
    color: #6B7280;
    margin: 0;
}

/* 快捷操作区域 */
.quick-actions,
.recent-activities {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    padding: 24px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    height: 100%;
}

.quick-actions h4,
.recent-activities h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--nav-text-color);
    margin: 0 0 20px 0;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--bg-light);
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
}

.action-buttons .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* 最近活动列表 */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.activity-item:hover {
    background: rgba(20, 94, 255, 0.05);
}

.activity-item i {
    width: 20px;
    text-align: center;
    font-size: 14px;
}

.activity-item span {
    flex: 1;
    font-size: 14px;
    color: var(--nav-text-color);
}

.activity-item small {
    font-size: 12px;
    color: #6B7280;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid transparent;
    cursor: pointer;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0F4FDB;
    border-color: #0F4FDB;
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-info {
    background-color: #06B6D4;
    color: white;
    border-color: #06B6D4;
}

.btn-info:hover {
    background-color: #0891B2;
    border-color: #0891B2;
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.btn-warning:hover {
    background-color: #D97706;
    border-color: #D97706;
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #DC2626;
    border-color: #DC2626;
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-outline-primary {
    background-color: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-light {
    background-color: var(--bg-white);
    color: var(--nav-text-color);
    border-color: var(--border-color);
}

.btn-light:hover {
    background-color: var(--bg-light);
    color: var(--nav-text-color);
}

/* 表格样式规范 - 彻底解决边框问题 */
.table {
    background: var(--bg-white);
    border-radius: var(--border-radius);
    table-layout: auto;
    width: 100%;
    border-collapse: collapse !important; /* 强制使用折叠边框模式 */
    border-spacing: 0;
}

/* 重置所有可能的表格边框样式 */
.table,
.table * {
    border-color: #E5E7EB !important;
}

/* 统一所有单元格边框 */
.table th,
.table td {
    border: 1px solid #E5E7EB !important;
    padding: 12px 15px;
    font-size: 14px;
    color: var(--nav-text-color);
}

/* 表头样式 - 修改背景色 */
.table thead tr {
    background-color: #E7EFFF; /* 新的表头背景色 */
}

.table th {
    background-color: #E7EFFF; /* 确保表头单元格也使用相同背景色 */
    font-weight: 600;
    white-space: nowrap;
}

/* 明确覆盖Bootstrap可能的默认样式 */
.table thead th,
.table tbody + tbody,
.table-bordered,
.table-bordered th,
.table-bordered td {
    border: 1px solid #E5E7EB !important;
}

/* 特别处理表头底部边框 */
.table thead th {
    border-bottom: 1px solid #E5E7EB !important;
    vertical-align: bottom;
}

/* 覆盖Bootstrap的表头底部双边框样式 */
.table-bordered thead th,
.table-bordered thead td,
.table > :not(:last-child) > :last-child > * {
    border-bottom-width: 1px !important;
    border-bottom-color: #E5E7EB !important;
}

/* 额外的高优先级规则，确保表头底部边框与其他边框一致 */
.table thead tr th,
.table thead tr td,
.table-bordered thead tr th,
.table-bordered thead tr td,
.table-striped thead tr th,
.table-striped thead tr td {
    border-bottom: 1px solid #E5E7EB !important;
    border-bottom-width: 1px !important;
    border-bottom-color: #E5E7EB !important;
}

/* 确保表体第一行的顶部边框与其他边框一致 */
.table tbody tr:first-child td,
.table tbody tr:first-child th {
    border-top: 1px solid #E5E7EB !important;
    border-top-width: 1px !important;
}

/* 表格容器样式 - 保留滚动功能 */
.table-container {
    border: 1px solid #E5E7EB;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    overflow: auto; /* 允许滚动 */
    background-color: transparent;
}

/* 当表格在容器内时，移除表格自身的边框，由容器提供边框 */
.table-container .table {
    border: none !important;
    margin-bottom: 0;
}



/* 表格行交替背景色 */
.table tbody tr:nth-child(odd) {
    background-color: var(--bg-white);
}

.table tbody tr:nth-child(even) {
    background-color: var(--bg-light);
}

/* 保持悬停效果 */
.table tbody tr:hover {
    background-color: rgba(20, 94, 255, 0.05); /* 稍微加深悬停背景色 */
}

.table td {
    padding: 12px 15px;
    font-size: 14px; /* 与表头字体大小一致 */
    color: var(--nav-text-color);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

/* 表格行悬停效果 */
.table tbody tr:hover {
    background-color: rgba(20, 94, 255, 0.02);
}

/* 最后一行底部边框 */
.table tbody tr:last-child td {
    border-bottom: 1px solid var(--border-color);
}

/* 表格列宽度控制 - 所有列统一规范 */
.table th,
.table td {
    max-width: 200px; /* 所有列最大宽度200px */
    min-width: 50px; /* 所有列最小宽度50px */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

}

/* 表格复选框和单选框居中样式 - 完整解决方案 */
.table td:first-child,
.table th:first-child {
    text-align: center;
    position: relative; /* 为绝对定位做准备 */
}

/* 复选框和单选框通用居中样式 */
.table td:first-child input[type="checkbox"],
.table th:first-child input[type="checkbox"],
.table td:first-child input[type="radio"],
.table th:first-child input[type="radio"] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
    padding: 0;
}

/* 表格序号列样式 */
.table th:nth-child(2),
.table td:nth-child(2) {
    text-align: center;
    width: 60px;
    min-width: 60px;
    max-width: 60px;
}

/* 序号列样式 */
.table td.row-number {
    font-weight: 500;
    color: var(--nav-text-color);
}

/* 更新前三列居中样式，包括序号列 */
.table th:nth-child(1),
.table td:nth-child(1),
.table th:nth-child(2),
.table td:nth-child(2),
.table th:nth-child(3),
.table td:nth-child(3),
.table th:last-child,
.table td:last-child {
    text-align: center;
}

/* 操作列特殊样式 - 确保有足够宽度 */
.table th:last-child,
.table td:last-child {
    min-width: 120px; /* 操作列最小宽度 */
    max-width: none; /* 移除最大宽度限制 */
    width: auto; /* 自动宽度 */
}

/* 数据列文本溢出处理 */
.table td:not(:first-child):not(:last-child) {
    cursor: pointer; /* 鼠标悬停提示 */
}

/* 表格单元格悬停提示 - 引用menu-text样式 */
.table td:not(:first-child):not(:last-child)[title]:hover::after {
    content: attr(title);
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(51, 51, 51, 0.95);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 4px;
    opacity: 0;
    animation: fadeInTooltip 0.2s ease forwards;
    pointer-events: none;
}

@keyframes fadeInTooltip {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 表格操作按钮 - 字体大小与表格单元格一致 */
.table-actions {
    display: flex;
    gap: 4px; /* 缩短操作链接之间的间距 */
    align-items: center;
    justify-content: center;
    white-space: nowrap; /* 确保操作链接始终不换行 */
    flex-wrap: nowrap; /* 禁止换行 */
}

.table-actions .btn {
    padding: 6px 12px;
    font-size: 14px; /* 与表格单元格字体大小一致 */
}

.table-actions a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px; /* 与表格单元格字体大小一致 */
    padding: 2px 6px; /* 缩小内边距，让操作列更紧凑 */
    border-radius: 3px;
    transition: var(--transition);
    white-space: nowrap; /* 确保单个操作链接不换行 */
    flex-shrink: 0; /* 防止操作链接被压缩 */
    display: inline-block; /* 确保链接为行内块元素 */
}

.table-actions a:hover {
    background-color: var(--primary-color-light);
}

.table-actions a.text-danger {
    color: var(--danger-color);
}

.table-actions a.text-danger:hover {
    background-color: var(--danger-color-light);
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--nav-text-color);
    font-size: 14px;
}

.form-label.required::before {
    content: "*";
    color: var(--danger-color);
    margin-right: 4px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background-color: var(--bg-white);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-color-light);
}

.form-control::placeholder {
    color: #9CA3AF;
}

/* 搜索框样式 */
.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-input {
    min-width: 200px;
}

/* 筛选器样式 */
.filters {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 20px;
    padding: 20px;
    background: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-item label {
    font-size: 12px;
    color: #6B7280;
    font-weight: 500;
}

.filter-item select,
.filter-item input {
    min-width: 120px;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 13px;
}

/* 状态标签 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.active {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.status-badge.inactive {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6B7280;
}

.status-badge.pending {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

/* 加载动画 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6B7280;
}

.loading i {
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6B7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h4 {
    font-size: 18px;
    margin-bottom: 8px;
    color: var(--nav-text-color);
}

.empty-state p {
    font-size: 14px;
    margin-bottom: 20px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    /* 移动端间距调整 */
    .page-header {
        margin-bottom: 16px; /* 移动端减少间距 */
    }

    .ant-card-body {
        padding: 16px; /* 移动端减少内边距 */
        margin-bottom: 16px;
    }

    .pagination-container {
        margin-top: 16px; /* 移动端减少间距 */
        margin-bottom: 16px;
    }

    .form-section {
        margin-bottom: 16px; /* 移动端减少表单区块间距 */
        padding: 16px; /* 移动端减少表单内边距 */
    }

    .form-actions {
        margin-top: 24px; /* 移动端减少表单操作按钮间距 */
        padding: 16px;
    }

    .stat-card {
        padding: 16px;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .stat-info h3 {
        font-size: 24px;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-item {
        width: 100%;
    }

    .filter-item select,
    .filter-item input {
        min-width: auto;
        width: 100%;
    }

    .table-actions {
        gap: 2px; /* 移动端稍微减少间距 */
        flex-wrap: nowrap; /* 确保不换行 */
    }
}
