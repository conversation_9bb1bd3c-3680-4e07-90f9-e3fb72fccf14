/* 医院药品管理系统 - 分页组件样式 */

/* 分页组件容器 */
.pagination-container {
    margin: 20px 0;
}

.pagination-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px 16px;
    background: var(--bg-white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

/* 分页信息 */
.pagination-info {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--nav-text-color);
    font-size: 14px;
}

.total-info {
    font-weight: 500;
}

.total-count {
    color: var(--primary-color);
    font-weight: 600;
}

/* 每页显示数量选择器 */
.page-size-selector {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--nav-text-color);
    font-size: 14px;
}

.page-size-selector label {
    margin: 0;
    white-space: nowrap;
}

.page-size-select {
    width: auto;
    min-width: 100px;
    border-color: var(--border-color);
    font-size: 13px;
}

.page-size-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
}

/* 分页导航 */
.pagination-nav .pagination {
    margin: 0;
}

.pagination .page-item .page-link {
    color: var(--nav-text-color);
    border-color: var(--border-color);
    padding: 6px 10px;
    font-size: 14px;
    border-radius: var(--border-radius);
    margin: 0 1px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 3px;
}

.pagination .page-item .page-link:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-light);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: var(--bg-light);
    border-color: var(--border-color);
    cursor: not-allowed;
}

/* 页码按钮容器 */
.page-numbers {
    display: flex;
    align-items: center;
    gap: 1px;
}

.page-numbers .page-item .page-link {
    min-width: 32px;
    text-align: center;
    justify-content: center;
}

/* 快速跳转 */
.quick-jump {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--nav-text-color);
    font-size: 14px;
}

.jump-input {
    width: 60px;
    text-align: center;
    border-color: var(--border-color);
    font-size: 13px;
}

.jump-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
}

.jump-btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-size: 13px;
    padding: 4px 12px;
}

.jump-btn:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 移动端分页样式 */
.mobile-pagination {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
}

.pagination-info-mobile {
    text-align: center;
    color: var(--nav-text-color);
    font-size: 14px;
    font-weight: 500;
}

.current-page,
.total-pages {
    color: var(--primary-color);
    font-weight: 600;
}

.pagination-nav-mobile .pagination {
    justify-content: center;
}

.pagination-nav-mobile .page-link {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.current-page-display {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    font-weight: 600;
}

.total-info-mobile {
    text-align: center;
    color: var(--top-nav-text-color);
    font-size: 13px;
}

/* 紧凑型分页样式 */
.pagination-container.compact .pagination-wrapper {
    padding: 8px 12px;
    gap: 6px;
}

.pagination-container.compact .pagination-info,
.pagination-container.compact .page-size-selector,
.pagination-container.compact .quick-jump {
    font-size: 13px;
}

.pagination-container.compact .page-link {
    padding: 6px 10px;
    font-size: 13px;
}

.pagination-container.compact .page-size-select,
.pagination-container.compact .jump-input {
    font-size: 12px;
}

.pagination-container.compact .jump-btn {
    font-size: 12px;
    padding: 3px 10px;
}

/* 响应式设计 */
@media (max-width: 991.98px) {
    .pagination-wrapper {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }
    
    .pagination-info,
    .page-size-selector,
    .quick-jump {
        justify-content: center;
    }
    
    .pagination-nav {
        display: flex;
        justify-content: center;
    }
    
    /* 隐藏桌面端的文字标签 */
    .pagination .page-link span {
        display: none !important;
    }
}

@media (max-width: 767.98px) {
    .pagination-wrapper {
        padding: 12px 16px;
    }
    
    /* 简化移动端显示 */
    .page-size-selector {
        order: 3;
    }
    
    .pagination-nav {
        order: 1;
    }
    
    .pagination-info {
        order: 2;
    }
    
    .quick-jump {
        display: none; /* 移动端隐藏快速跳转 */
    }
    
    /* 移动端页码按钮样式调整 */
    .page-numbers .page-item .page-link {
        min-width: 32px;
        padding: 6px 8px;
        font-size: 13px;
    }
    
    /* 限制显示的页码数量 */
    .page-numbers .page-item:nth-child(n+6) {
        display: none;
    }
}

@media (max-width: 575.98px) {
    .pagination-wrapper {
        padding: 10px 12px;
        gap: 8px;
    }
    
    .pagination-info,
    .page-size-selector {
        font-size: 13px;
    }
    
    .page-link {
        padding: 5px 8px !important;
        font-size: 12px !important;
    }
    
    .page-size-select {
        min-width: 80px;
        font-size: 12px;
    }
    
    /* 进一步限制页码显示 */
    .page-numbers .page-item:nth-child(n+4) {
        display: none;
    }
}

/* 演示页面样式 */
.pagination-demo {
    padding: 20px;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.pagination-demo h4 {
    color: var(--nav-text-color);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

/* 加载状态 */
.pagination-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pagination-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
