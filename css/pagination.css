/* 医院药品管理系统 - 分页组件样式 */

/* 分页组件容器 */
.pagination-container {
    margin: 10px 0 10px 0;
    width: 100%;
}

.pagination-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: nowrap;
    gap: 15px;
    padding: 0;
    background: transparent;
    border: none;
    box-shadow: none;
}

/* 分页信息 */
.pagination-info {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--nav-text-color);
    font-size: 13px;
    white-space: nowrap;
    flex-shrink: 0;
}

.total-info {
    font-weight: 500;
}

.total-count {
    color: var(--primary-color);
    font-weight: 600;
}

/* 每页显示数量选择器 */
.page-size-selector {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--nav-text-color);
    font-size: 13px;
    white-space: nowrap;
    flex-shrink: 0;
}

.page-size-selector label {
    margin: 0;
    white-space: nowrap;
}

.page-size-select {
    width: auto;
    min-width: 100px;
    border-color: var(--border-color);
    font-size: 13px;
}

.page-size-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
}

/* 分页导航 */
.pagination-nav .pagination {
    margin: 0;
}

.pagination .page-item .page-link {
    color: var(--nav-text-color);
    border-color: var(--border-color);
    padding: 6px 10px;
    font-size: 14px;
    border-radius: var(--border-radius);
    margin: 0 2px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 3px;
}

.pagination .page-item .page-link:hover {
    color: var(--primary-color);
    background-color: var(--primary-color-light);
    border-color: var(--primary-color);
}

.pagination .page-item.active .page-link {
    color: white;
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: var(--bg-light);
    border-color: var(--border-color);
    cursor: not-allowed;
}

/* 页码按钮容器 */
.page-numbers {
    display: flex;
    align-items: center;
    gap: 1px;
}

.page-numbers .page-item .page-link {
    min-width: 32px;
    text-align: center;
    justify-content: center;
}

/* 快速跳转 */
.quick-jump {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--nav-text-color);
    font-size: 13px;
    white-space: nowrap;
    flex-shrink: 0;
}

.jump-input {
    width: 60px;
    text-align: center;
    border-color: var(--border-color);
    font-size: 13px;
}

.jump-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-light);
}

.jump-btn {
    border-color: var(--primary-color);
    color: var(--primary-color);
    font-size: 13px;
    padding: 4px 12px;
}

.jump-btn:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* 移动端分页样式 */
.mobile-pagination {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
}

.pagination-info-mobile {
    text-align: center;
    color: var(--nav-text-color);
    font-size: 14px;
    font-weight: 500;
}

.current-page,
.total-pages {
    color: var(--primary-color);
    font-weight: 600;
}

.pagination-nav-mobile .pagination {
    justify-content: center;
}

.pagination-nav-mobile .page-link {
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.current-page-display {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
    color: white !important;
    font-weight: 600;
}

.total-info-mobile {
    text-align: center;
    color: var(--top-nav-text-color);
    font-size: 13px;
}

/* 紧凑型分页样式 */
.pagination-container.compact .pagination-wrapper {
    padding: 8px 12px;
    gap: 6px;
}

.pagination-container.compact .pagination-info,
.pagination-container.compact .page-size-selector,
.pagination-container.compact .quick-jump {
    font-size: 13px;
}

.pagination-container.compact .page-link {
    padding: 6px 10px;
    font-size: 13px;
}

.pagination-container.compact .page-size-select,
.pagination-container.compact .jump-input {
    font-size: 12px;
}

.pagination-container.compact .jump-btn {
    font-size: 12px;
    padding: 3px 10px;
}

/* 响应式设计 */
@media (max-width: 991.98px) {
    .pagination-wrapper {
        flex-wrap: nowrap;
        gap: 4px;
        overflow-x: auto;
    }

    .pagination-info,
    .page-size-selector,
    .quick-jump {
        font-size: 12px;
    }

    .pagination-nav {
        flex-shrink: 0;
    }

    /* 隐藏桌面端的文字标签 */
    .pagination .page-link span {
        display: none !important;
    }
}

@media (max-width: 767.98px) {
    .pagination-wrapper {
        padding: 8px 0;
        gap: 3px;
    }

    .pagination-info,
    .page-size-selector {
        font-size: 11px;
    }

    .quick-jump {
        display: none; /* 移动端隐藏快速跳转 */
    }

    /* 移动端页码按钮样式调整 */
    .page-numbers .page-item .page-link {
        min-width: 28px;
        padding: 4px 6px;
        font-size: 12px;
    }

    /* 限制显示的页码数量 */
    .page-numbers .page-item:nth-child(n+6) {
        display: none;
    }
}

@media (max-width: 575.98px) {
    .pagination-wrapper {
        padding: 6px 0;
        gap: 2px;
    }

    .pagination-info,
    .page-size-selector {
        font-size: 10px;
    }

    .page-link {
        padding: 3px 5px !important;
        font-size: 11px !important;
    }

    .page-size-select {
        min-width: 60px;
        font-size: 10px;
    }

    /* 进一步限制页码显示 */
    .page-numbers .page-item:nth-child(n+4) {
        display: none;
    }
}

/* 演示页面样式 */
.pagination-demo {
    padding: 20px;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    margin-bottom: 20px;
}

.pagination-demo h4 {
    color: var(--nav-text-color);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
}

/* 加载状态 */
.pagination-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pagination-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}
